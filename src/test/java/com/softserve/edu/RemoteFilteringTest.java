package com.softserve.edu;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.junit.jupiter.api.*;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;
import java.util.List;

public class RemoteFilteringTest {
    private static final String BASE_URL
            = "https://devexpress.github.io/devextreme-reactive/react/grid/docs/guides/filtering/";
    private static final Long IMPLICITLY_WAIT_SECONDS = 10L;
    private static WebDriver driver;
    private  static WebDriverWait wait;

    @BeforeAll
    public static void beforeAll() {
        WebDriverManager.chromedriver().setup();
        driver = new ChromeDriver();
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(IMPLICITLY_WAIT_SECONDS));
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        driver.manage().window().maximize();

    }

    @AfterAll
    public static void afterAll() {
        if (driver != null) {
            driver.quit();
        }
    }

    @BeforeEach
    public void beforeEach() {
        driver.get(BASE_URL);
        closePopup();
    }

    @AfterEach
    public void afterEach() {
        // logout; clear cache; delete cookie; delete session;
        // Save Screen;
    }

    private void closePopup() {
        List<WebElement> footerButton = driver
                .findElements(By.xpath("//footer[contains(@class,'cookie')]//button"));
        System.out.println("footerButton.size() = " + footerButton.size());
        if (footerButton.size() > 0) {
            footerButton.get(0).click();
        }
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(IMPLICITLY_WAIT_SECONDS));
    }

    private void scrollToElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
    }

    @Test
    public void checkRemoteFiltering() {
        WebElement titleRemoteFiltering = driver.findElement(By.id("remote-filtering"));
        scrollToElement(titleRemoteFiltering);

        WebElement dataTableFrame = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath("//h2[@id='remote-filtering']/following::iframe[1]")));
        driver.switchTo().frame(dataTableFrame);

        WebElement cityFilterInput = wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector("thead tr:nth-of-type(2) th:nth-of-type(2) input")));
        cityFilterInput.clear();
        cityFilterInput.sendKeys("lon");


        wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector("tbody tr")));

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String countryText = wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector("tbody tr:first-child td:nth-of-type(1)"))).getText();
        String cityText = driver.findElement(By.cssSelector("tbody tr:first-child td:nth-of-type(2)")).getText();
        String addressText = driver.findElement(By.cssSelector("tbody tr:first-child td:nth-of-type(3)")).getText();

        Assertions.assertEquals("UK", countryText);
        Assertions.assertEquals("London", cityText);
        Assertions.assertEquals("Fauntleroy Circus", addressText);

        driver.switchTo().defaultContent();
    }

}
