package com.softserve.edu;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.junit.jupiter.api.*;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;

public class ClubSearchTest {


    private static final String BASE_URL
            = "http://speak-ukrainian.eastus2.cloudapp.azure.com/dev/";
    private static final Long IMPLICITLY_WAIT_SECONDS = 10L;
    private static WebDriver driver;
    private static WebDriverWait wait;

    @BeforeAll
    public static void beforeAll() {
        WebDriverManager.chromedriver().setup();
        driver = new ChromeDriver();
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(IMPLICITLY_WAIT_SECONDS));
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        driver.manage().window().maximize();
    }

    @AfterAll
    public static void afterAll() {
        if (driver != null) {
            driver.quit();
        }
    }

    @BeforeEach
    public void beforeEach() {
        driver.get(BASE_URL);
    }

    @AfterEach
    public void afterEach() {
        // logout; clear cache; delete cookie; delete session;
        // Save Screen;
    }



//    @Test
//    public void checkClubSearch() {
//
//    }

    @Test
    public void clickChooseCity(){
        WebElement dropDownTriggerChooseCity = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//div[contains(@class, 'ant-dropdown-trigger')]")));
        dropDownTriggerChooseCity.click();
        Assertions.assertTrue(dropDownTriggerChooseCity.isDisplayed());

        WebElement dropDownMenuChooseCity = wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector(".ant-dropdown-menu")));
        Assertions.assertTrue(dropDownMenuChooseCity.isDisplayed());
    }

    @Test
    public void chooseKharkiv(){
        clickChooseCity();

        WebElement kharkiv = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//li[contains(@class, 'ant-dropdown-menu-item')]//span[text()='Харків']")));
        kharkiv.click();

        WebElement cityName = wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector(".city-name")));
        Assertions.assertEquals("Гуртки в місті Харків", cityName.getText());
    }

}
